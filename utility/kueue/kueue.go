package kueue

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"mlops/internal/consts"
	"mlops/internal/model/dto"
	"mlops/tools/client"
	"mlops/tools/gpu"
	"mlops/tools/gpu/strategies"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kueuev1alpha1 "sigs.k8s.io/kueue/apis/kueue/v1alpha1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

// Kueue 相关常量
const (
	ClusterQueuePrefix      = "mlops-team-"
	LocalQueuePrefix        = "mlops-team-"
	ResourceFlavorPrefix    = "mlops-gpu-"
	ManagedByLabel          = "managed-by"
	ManagedByValue          = "mlops"
	TeamIdLabel             = "mlops.ttyuyin.com/team-id"
	GpuFullNameLabel        = "mlops.ttyuyin.com/gpu-fullname"
	PriorityLabel           = "mlops.ttyuyin.com/priority"
	ModelTypeLabel          = "mlops.ttyuyin.com/model-type"
	MemberLabel             = "mlops.ttyuyin.com/member"
	NodeLabelGpuModel       = "gpu-model"
	TolerationKey           = "pool-type"
	TolerationValue         = "gpu"
	CohortName              = "mlops-shared"
	QueueingStrategy        = "BestEffortFIFO"
	FixedCpuQuota           = "999"
	FixedMemoryQuota        = "2Ti"
	FixedEniIpQuota         = "999"
	LendingLimitPercent     = 20
	BizTolerationKey        = "biz"
	BizTolerationValue      = "mlops"
	FixedPendingTimeSeconds = 500

	// Kueue 工作负载标签
	QueueNameLabel     = "kueue.x-k8s.io/queue-name"
	PriorityClassLabel = "kueue.x-k8s.io/priority-class"
	JobUidLabel        = "kueue.x-k8s.io/job-uid"
)

// Kueue Resource Kind
const (
	ClusterQueueKind          = "ClusterQueue"
	LocalQueueKind            = "LocalQueue"
	ResourceFlavorKind        = "ResourceFlavor"
	WorkloadPriorityClassKind = "WorkloadPriorityClass"
	CohortKind                = "Cohort"
	WorkloadKind              = "Workload"
)

// 优先级映射
var PriorityValueMap = map[string]int32{
	"P0": 1900,
	"P1": 1800,
	"P2": 1700,
	"P3": 1600,
}

var GPUTypeMap = map[string]string{
	"NVIDIA L20":            "L20",
	"Tesla A100 80G":        "A100",
	"NVIDIA A800-SXM4-80GB": "A800",
	"NVIDIA H20":            "H20",
}

var GPUModelMap = map[string]string{
	"l20":  "NVIDIA L20",
	"a100": "Tesla A100 80G",
	"a800": "NVIDIA A800-SXM4-80GB",
	"h20":  "NVIDIA H20",
}

// GetClusterType 获取集群类型
func GetClusterType(clusterName string) string {
	switch {
	case strings.HasPrefix(clusterName, "k8s-tc"):
		return consts.ClusterTypeTencent
	case strings.HasPrefix(clusterName, "k8s-ali"):
		return consts.ClusterTypeAliyun
	case strings.HasPrefix(clusterName, "k8s-hw"):
		return consts.ClusterTypeHuawei
	case strings.HasPrefix(clusterName, "k8s-hs"):
		return consts.ClusterTypeVolcengine
	default:
		return ""
	}
}

// UpdateClusterKueueResources 更新单个集群的 Kueue 资源
func UpdateClusterKueueResources(ctx context.Context, teamId int, clusterName string, namespaces []string, quotas []*dto.GpuQuota) error {
	// 1. 确认 namespaces 标签
	for _, namespace := range namespaces {
		err := EnsureNamespaceLabel(ctx, clusterName, namespace)
		if err != nil {
			return fmt.Errorf("确认 namespace %s 标签失败: %w", namespace, err)
		}
	}

	// 2. 确认 Cohort 资源
	err := EnsureCohort(ctx, clusterName)
	if err != nil {
		return fmt.Errorf("确认 Cohort 失败: %w", err)
	}

	// 3. 确认 ResourceFlavor 资源
	resourceFlavorMap, err := EnsureResourceFlavors(ctx, clusterName, quotas)
	if err != nil {
		return fmt.Errorf("确认 ResourceFlavor 失败: %w", err)
	}

	// 4. 更新或创建 ClusterQueue
	err = UpdateOrCreateClusterQueue(ctx, teamId, clusterName, resourceFlavorMap)
	if err != nil {
		return fmt.Errorf("更新 ClusterQueue 失败: %w", err)
	}

	return nil
}

// EnsureResourceFlavors 确保 ResourceFlavor 存在
func EnsureResourceFlavors(ctx context.Context, clusterName string, quotas []*dto.GpuQuota) (map[string]*dto.GpuQuota, error) {
	resourceFlavorMap := make(map[string]*dto.GpuQuota)

	for _, quota := range quotas {
		// 构造 GPU 全名标签值
		gpuFullName := strings.ReplaceAll(quota.GpuType, " ", "-")

		// 提取 GPU 型号
		gpuModel, ok := GPUTypeMap[quota.GpuType]
		if !ok {
			return resourceFlavorMap, fmt.Errorf("无法提取 GPU 型号: %s", quota.GpuType)
		}

		resourceFlavorName := ResourceFlavorPrefix + strings.ToLower(gpuModel)

		// 尝试获取现有的 ResourceFlavor
		existingRF, err := client.GetResourceFlavor(ctx, clusterName, resourceFlavorName)
		if err != nil || existingRF == nil {
			// 创建新的 ResourceFlavor
			err = CreateResourceFlavor(ctx, clusterName, resourceFlavorName, gpuFullName, gpuModel)
			if err != nil {
				return resourceFlavorMap, fmt.Errorf("创建 ResourceFlavor %s 失败: %w", resourceFlavorName, err)
			}
		}

		resourceFlavorMap[resourceFlavorName] = quota
	}

	return resourceFlavorMap, nil
}

// CreateResourceFlavor 创建 ResourceFlavor
func CreateResourceFlavor(ctx context.Context, clusterName, name, gpuFullName, gpuModel string) error {
	resourceFlavor := &kueuev1beta1.ResourceFlavor{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       ResourceFlavorKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				ManagedByLabel:   ManagedByValue,
				GpuFullNameLabel: gpuFullName,
			},
		},
		Spec: kueuev1beta1.ResourceFlavorSpec{
			NodeLabels: map[string]string{
				NodeLabelGpuModel: strings.ToLower(gpuModel),
			},
			Tolerations: []v1.Toleration{
				{
					Key:      TolerationKey,
					Operator: v1.TolerationOpEqual,
					Value:    TolerationValue,
					Effect:   v1.TaintEffectNoSchedule,
				},
				{
					Key:      BizTolerationKey,
					Operator: v1.TolerationOpEqual,
					Value:    BizTolerationValue,
					Effect:   v1.TaintEffectNoSchedule,
				},
			},
		},
	}

	return client.CreateResourceFlavor(ctx, clusterName, resourceFlavor)
}

// UpdateOrCreateClusterQueue 更新或创建 ClusterQueue
func UpdateOrCreateClusterQueue(ctx context.Context, teamId int, clusterName string, resourceFlavorMap map[string]*dto.GpuQuota) error {
	clusterQueueName := ClusterQueuePrefix + strconv.Itoa(teamId)

	// 获取集群类型对应的资源名称
	clusterType := GetClusterType(clusterName)
	converter := gpu.GetStrategy(clusterType)

	// 定义固定的资源顺序
	var coveredResources []v1.ResourceName
	if len(resourceFlavorMap) > 0 {
		// 使用固定的资源顺序，而不是依赖 map 遍历
		baseResources := []v1.ResourceName{
			v1.ResourceName(strategies.OAM_GPU_CORE),
			v1.ResourceName(strategies.OAM_GPU_MEM),
			v1.ResourceCPU,
			v1.ResourceMemory,
			v1.ResourceName(strategies.OAM_ENI_IP),
		}

		// 取第一个 quota 来确定哪些资源会被转换器修改
		var firstQuota *dto.GpuQuota
		for _, quota := range resourceFlavorMap {
			firstQuota = quota
			break
		}

		// 构建标准的资源映射来确定最终的资源类型
		resourcesMap := make(map[v1.ResourceName]resource.Quantity)
		resourcesMap[v1.ResourceName(strategies.OAM_GPU_CORE)], _ = resource.ParseQuantity(strconv.Itoa(int(firstQuota.Nums) * 100))
		resourcesMap[v1.ResourceName(strategies.OAM_GPU_MEM)], _ = resource.ParseQuantity(firstQuota.GpuMemory)
		resourcesMap[v1.ResourceCPU] = resource.MustParse(FixedCpuQuota)
		resourcesMap[v1.ResourceMemory] = resource.MustParse(FixedMemoryQuota)
		resourcesMap[v1.ResourceName(strategies.OAM_ENI_IP)] = resource.MustParse(FixedEniIpQuota)
		if converter != nil {
			converter.Convert(resourcesMap)
		}

		// 按照基础资源的固定顺序，检查哪些资源存在于转换后的映射中
		for _, resourceName := range baseResources {
			if _, exists := resourcesMap[resourceName]; exists {
				coveredResources = append(coveredResources, resourceName)
			}
		}

		// 添加转换器可能新增的资源（按字母顺序排序以保证一致性）
		var additionalResources []v1.ResourceName
		for resourceName := range resourcesMap {
			found := false
			for _, baseResource := range baseResources {
				if resourceName == baseResource {
					found = true
					break
				}
			}
			if !found {
				additionalResources = append(additionalResources, resourceName)
			}
		}
		// 对额外资源进行排序以保证一致性
		sort.Slice(additionalResources, func(i, j int) bool {
			return string(additionalResources[i]) < string(additionalResources[j])
		})
		coveredResources = append(coveredResources, additionalResources...)
	}

	var flavors []kueuev1beta1.FlavorQuotas
	for resourceFlavorName, quota := range resourceFlavorMap {
		// 不同集群类型的资源大小转化
		resourcesMap := make(map[v1.ResourceName]resource.Quantity)
		resourcesMap[v1.ResourceName(strategies.OAM_GPU_CORE)], _ = resource.ParseQuantity(strconv.Itoa(int(quota.Nums) * 100))
		resourcesMap[v1.ResourceName(strategies.OAM_GPU_MEM)], _ = resource.ParseQuantity(quota.GpuMemory)
		resourcesMap[v1.ResourceCPU] = resource.MustParse(FixedCpuQuota)
		resourcesMap[v1.ResourceMemory] = resource.MustParse(FixedMemoryQuota)
		resourcesMap[v1.ResourceName(strategies.OAM_ENI_IP)] = resource.MustParse(FixedEniIpQuota)
		if converter != nil {
			converter.Convert(resourcesMap)
		}

		// 严格按照 coveredResources 的顺序构建 resourceQuotas
		var resourceQuotas []kueuev1beta1.ResourceQuota
		for _, resourceName := range coveredResources {
			if resourceQuota, exists := resourcesMap[resourceName]; exists {
				// 计算 LendingLimit (NominalQuota 的 20%)，使用格式化函数
				lendingLimit := formatLendingLimit(resourceQuota, LendingLimitPercent)

				resourceQuotas = append(resourceQuotas, kueuev1beta1.ResourceQuota{
					Name:         resourceName,
					NominalQuota: resourceQuota,
					LendingLimit: &lendingLimit,
				})
			}
		}

		flavor := kueuev1beta1.FlavorQuotas{
			Name:      kueuev1beta1.ResourceFlavorReference(resourceFlavorName),
			Resources: resourceQuotas,
		}
		flavors = append(flavors, flavor)
	}

	clusterQueue := &kueuev1beta1.ClusterQueue{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       ClusterQueueKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: clusterQueueName,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
				TeamIdLabel:    strconv.Itoa(teamId),
			},
		},
		Spec: kueuev1beta1.ClusterQueueSpec{
			NamespaceSelector: &metav1.LabelSelector{},
			Cohort:            CohortName,
			QueueingStrategy:  kueuev1beta1.BestEffortFIFO,
		},
	}

	if len(coveredResources) > 0 {
		clusterQueue.Spec.ResourceGroups = []kueuev1beta1.ResourceGroup{
			{
				CoveredResources: coveredResources,
				Flavors:          flavors,
			},
		}
	}

	// 尝试获取现有的 ClusterQueue
	existingCQ, err := client.GetClusterQueue(ctx, clusterName, clusterQueueName)
	if err != nil || existingCQ == nil {
		// 创建新的 ClusterQueue
		return client.CreateClusterQueue(ctx, clusterName, clusterQueue)
	} else {
		// 更新现有的 ClusterQueue
		existingCQ.Spec = clusterQueue.Spec
		return client.UpdateClusterQueue(ctx, clusterName, existingCQ)
	}
}

// formatLendingLimit 格式化 LendingLimit，使其具有合适的单位
func formatLendingLimit(nominalQuota resource.Quantity, percent int) resource.Quantity {
	// 计算原始值
	originalValue := nominalQuota.Value() * int64(percent) / 100

	// 根据资源类型和大小选择合适的单位
	nominalStr := nominalQuota.String()

	// 内存相关资源
	if strings.Contains(nominalStr, "Gi") || strings.Contains(nominalStr, "Ti") || strings.Contains(nominalStr, "Mi") {
		// 如果原始值大于等于1Ti，使用Ti
		if originalValue >= 1024*1024*1024*1024 {
			tiValue := originalValue / (1024 * 1024 * 1024 * 1024)
			return resource.MustParse(fmt.Sprintf("%dTi", tiValue))
		}
		// 如果原始值大于等于1Gi，使用Gi
		if originalValue >= 1024*1024*1024 {
			giValue := originalValue / (1024 * 1024 * 1024)
			return resource.MustParse(fmt.Sprintf("%dGi", giValue))
		}
		// 否则使用Mi
		miValue := originalValue / (1024 * 1024)
		return resource.MustParse(fmt.Sprintf("%dMi", miValue))
	}

	// CPU 相关资源（通常是毫核）
	if strings.Contains(nominalStr, "m") || nominalQuota.MilliValue() > nominalQuota.Value()*1000 {
		// 如果结果可以整除1000，使用核心数
		if originalValue%1000 == 0 {
			return resource.MustParse(fmt.Sprintf("%d", originalValue/1000))
		}
		// 否则使用毫核
		return resource.MustParse(fmt.Sprintf("%dm", originalValue))
	}

	// GPU 相关资源或其他整数资源
	return resource.MustParse(fmt.Sprintf("%d", originalValue))
}

// EnsureWorkloadPriorityClass 确保 WorkloadPriorityClass 存在
func EnsureWorkloadPriorityClass(ctx context.Context, clusterName, priority string) error {
	// 构造名称
	name := CohortName + "-" + strings.ToLower(priority)

	// 尝试获取现有的 WorkloadPriorityClass
	existingWPC, err := client.GetWorkloadPriorityClass(ctx, clusterName, name)
	if err != nil || existingWPC == nil {
		// 创建新的 WorkloadPriorityClass
		return CreateWorkloadPriorityClass(ctx, clusterName, priority)
	}

	return nil
}

// CreateWorkloadPriorityClass 创建 WorkloadPriorityClass
func CreateWorkloadPriorityClass(ctx context.Context, clusterName, priority string) error {
	// 获取优先级对应的值
	value, exists := PriorityValueMap[priority]
	if !exists {
		return fmt.Errorf("不支持的优先级: %s", priority)
	}

	// 构造名称
	name := CohortName + "-" + strings.ToLower(priority)

	workloadPriorityClass := &kueuev1beta1.WorkloadPriorityClass{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       WorkloadPriorityClassKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
				PriorityLabel:  priority,
			},
		},
		Value: value,
	}

	return client.CreateWorkloadPriorityClass(ctx, clusterName, workloadPriorityClass)
}

// EnsureLocalQueue 确保 LocalQueue 存在
func EnsureLocalQueue(ctx context.Context, clusterName, namespace string, teamId int, modelTypeIndex ModelTypeIndex) error {
	// 构造 LocalQueue 名称
	localQueueName := LocalQueuePrefix + strconv.Itoa(teamId) + "-" + strconv.Itoa(int(modelTypeIndex))

	// 尝试获取现有的 LocalQueue
	existingLQ, err := client.GetLocalQueue(ctx, clusterName, namespace, localQueueName)
	if err != nil || existingLQ == nil {
		// 创建新的 LocalQueue
		return CreateLocalQueue(ctx, clusterName, namespace, teamId, modelTypeIndex)
	}

	return nil
}

// CreateLocalQueue 创建 LocalQueue
func CreateLocalQueue(ctx context.Context, clusterName, namespace string, teamId int, modelTypeIndex ModelTypeIndex) error {
	// 构造 LocalQueue 名称
	localQueueName := LocalQueuePrefix + strconv.Itoa(teamId) + "-" + strconv.Itoa(int(modelTypeIndex))

	// 构造 ClusterQueue 名称
	clusterQueueName := ClusterQueuePrefix + strconv.Itoa(teamId)

	// 获取模型类型值
	modelTypeValue, exists := ModelTypeMaps[modelTypeIndex]
	if !exists {
		return fmt.Errorf("不支持的模型类型索引: %d", modelTypeIndex)
	}

	localQueue := &kueuev1beta1.LocalQueue{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       LocalQueueKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      localQueueName,
			Namespace: namespace,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
				TeamIdLabel:    strconv.Itoa(teamId),
				ModelTypeLabel: modelTypeValue,
			},
		},
		Spec: kueuev1beta1.LocalQueueSpec{
			ClusterQueue: kueuev1beta1.ClusterQueueReference(clusterQueueName),
		},
	}

	return client.CreateLocalQueue(ctx, clusterName, localQueue)
}

// DeleteLocalQueue 删除 LocalQueue
func DeleteLocalQueue(ctx context.Context, clusterName, namespace string, teamId int, modelTypeIndex ModelTypeIndex) error {
	// 构造 LocalQueue 名称
	localQueueName := LocalQueuePrefix + strconv.Itoa(teamId) + "-" + strconv.Itoa(int(modelTypeIndex))

	// 尝试获取现有的 LocalQueue
	existingLQ, err := client.GetLocalQueue(ctx, clusterName, namespace, localQueueName)
	if err != nil || existingLQ == nil {
		// LocalQueue 不存在，直接返回
		return nil
	}

	// 删除 LocalQueue
	return client.DeleteLocalQueue(ctx, clusterName, namespace, localQueueName)
}

// EnsureCohort 确保 Cohort 存在
func EnsureCohort(ctx context.Context, clusterName string) error {
	// 尝试获取现有的 Cohort
	existingCohort, err := client.GetCohort(ctx, clusterName, CohortName)
	if err != nil || existingCohort == nil {
		// 创建新的 Cohort
		return CreateCohort(ctx, clusterName)
	}
	return nil
}

// CreateCohort 创建 Cohort
func CreateCohort(ctx context.Context, clusterName string) error {
	cohort := &kueuev1alpha1.Cohort{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueAlphaVersion,
			Kind:       CohortKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: CohortName,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
			},
		},
	}

	return client.CreateCohort(ctx, clusterName, cohort)
}

// UpdateWorkloadPriority 更新 Workload 的优先级
func UpdateWorkloadPriority(ctx context.Context, clusterName, namespace, jobUid, priority string) error {
	// 获取优先级对应的值
	priorityValue, exists := PriorityValueMap[priority]
	if !exists {
		return fmt.Errorf("不支持的优先级: %s", priority)
	}

	// 通过 job-uid 标签查找对应的 Workload
	workloads, err := client.ListWorkload(
		ctx, clusterName, namespace, []constack_openapi.LabelSelector{
			{Key: JobUidLabel, Op: "=", Value: jobUid}})
	if err != nil {
		return fmt.Errorf("查找 Workload 失败: %w", err)
	}

	if len(workloads) == 0 {
		return fmt.Errorf("未找到对应的 Workload，job-uid: %s", jobUid)
	}

	if len(workloads) > 1 {
		return fmt.Errorf("找到多个 Workload，job-uid: %s", jobUid)
	}

	workload := &workloads[0]

	// // 检查 Workload 是否已经被 admitted
	// for _, condition := range workload.Status.Conditions {
	// 	if condition.Type == "Admitted" && condition.Status == metav1.ConditionTrue {
	// 		return fmt.Errorf("Workload 已被 admitted 正在运行，不允许修改优先级")
	// 	}
	// }

	// 更新优先级
	workload.Spec.Priority = &priorityValue

	// 更新 Workload
	err = client.UpdateWorkload(ctx, clusterName, workload)
	if err != nil {
		return fmt.Errorf("更新 Workload 优先级失败: %w", err)
	}

	return nil
}

// GetClusterGpuAllocatableResources 获取集群GPU可分配资源
// 入参: clusters []string - 集群名称列表
// 出参: map[string]float64 - key为GPU简称(如l20)，value为GPU卡数
func GetClusterGpuAllocatableResources(ctx context.Context, clusters []string) (map[string]float64, error) {
	result := make(map[string]float64)

	for _, clusterName := range clusters {
		// 获取集群类型
		clusterType := GetClusterType(clusterName)
		if clusterType == "" {
			log.L.WithName("GetClusterGpuAllocatableResources").Warningf(ctx, "Unknown cluster type for cluster: %s", clusterName)
			continue
		}

		// 根据集群类型获取GPU资源标签
		gpuResourceKey := GetGpuResourceKeyByType(clusterType)
		if gpuResourceKey == "" {
			log.L.WithName("GetClusterGpuAllocatableResources").Warningf(ctx, "No GPU resource key for cluster type: %s", clusterType)
			continue
		}

		// 构建标签选择器，查找有gpu-model标签的节点
		labelSelectors := []constack_openapi.LabelSelector{
			{
				Key: NodeLabelGpuModel,
				Op:  "Exists",
			},
		}

		// 调用ListNode函数获取GPU节点
		nodes, err := client.ListNode(ctx, clusterName, labelSelectors)
		if err != nil {
			log.L.WithName("GetClusterGpuAllocatableResources").Errorf(ctx, "Failed to list nodes for cluster %s: %s", clusterName, err.Error())
			continue
		}

		// 循环处理每个节点
		for _, node := range nodes {
			// 检查节点是否有必需的容忍
			if !hasRequiredGpuToleration(node) {
				continue
			}

			// 获取gpu-model标签值
			gpuModel, exists := node.Labels[NodeLabelGpuModel]
			if !exists {
				continue
			}

			// 获取GPU资源的可分配数量
			allocatableGpu, exists := node.Status.Allocatable[v1.ResourceName(gpuResourceKey)]
			if !exists {
				continue
			}

			// 将GPU资源值除以100并累加到结果中
			gpuValue := allocatableGpu.Value()
			gpuCards := float64(gpuValue) / 100.0

			result[gpuModel] += gpuCards
		}
	}

	return toGPUType(result), nil
}

// GetGpuResourceKeyByType 根据集群类型获取GPU资源标签
func GetGpuResourceKeyByType(clusterType string) string {
	switch clusterType {
	case consts.ClusterTypeTencent:
		return strategies.KEY_QGPU_CORE
	case consts.ClusterTypeAliyun:
		return strategies.KEY_ALI_GPU_CORE
	case consts.ClusterTypeHuawei:
		return strategies.KEY_HUAWEI_GPU_CORE
	case consts.ClusterTypeVolcengine:
		return strategies.KEY_HS_GPU_CORE
	default:
		return ""
	}
}

// hasRequiredGpuToleration 检查节点是否有必需的GPU容忍
func hasRequiredGpuToleration(node v1.Node) bool {
	for _, taint := range node.Spec.Taints {
		if taint.Key == TolerationKey &&
			taint.Value == TolerationValue &&
			taint.Effect == v1.TaintEffectNoSchedule {
			return true
		}
	}
	return false
}

// GetClusterGpuUsedResources 获取多个集群的所有带有managed-by=mlops标签的ClusterQueue的在使用的GPU资源
// 入参: clusters []string - 集群名称列表, teamId int - 团队ID(可选，0表示不过滤团队)
// 出参: map[string]float64 - key为GPU简称(如l20)，value为GPU卡数(total除以100)
func GetClusterGpuUsedResources(ctx context.Context, clusters []string, teamId int) (map[string]float64, error) {
	result := make(map[string]float64)

	for _, cluster := range clusters {
		// 获取集群类型
		clusterType := GetClusterType(cluster)
		if clusterType == "" {
			log.L.WithName("GetClusterGpuUsedResources").Warningf(ctx, "Unknown cluster type for cluster: %s", cluster)
			continue
		}

		// 根据集群类型获取GPU资源标签
		gpuResourceKey := GetGpuResourceKeyByType(clusterType)
		if gpuResourceKey == "" {
			log.L.WithName("GetClusterGpuUsedResources").Warningf(ctx, "No GPU resource key for cluster type: %s", clusterType)
			continue
		}

		// 构建标签选择器，直接获取带有managed-by=mlops标签的ClusterQueue
		labelSelectors := []constack_openapi.LabelSelector{
			{
				Key:   ManagedByLabel,
				Op:    "=",
				Value: ManagedByValue,
			},
		}

		// 如果指定了teamId，添加团队标签过滤
		if teamId > 0 {
			labelSelectors = append(labelSelectors, constack_openapi.LabelSelector{
				Key:   TeamIdLabel,
				Op:    "=",
				Value: fmt.Sprintf("%d", teamId),
			})
		}

		// 获取带有指定标签的ClusterQueue
		clusterQueues, err := client.ListClusterQueue(ctx, cluster, labelSelectors)
		if err != nil {
			log.L.WithName("GetClusterGpuUsedResources").Errorf(ctx, "Failed to list cluster queues for cluster %s: %s", cluster, err.Error())
			continue
		}

		// 循环处理每个ClusterQueue的status.flavorsUsage
		for _, cq := range clusterQueues {
			if cq.Status.FlavorsUsage == nil {
				continue
			}

			for _, flavorUsage := range cq.Status.FlavorsUsage {
				// 检查flavor name是否以ResourceFlavorPrefix开头
				flavorName := string(flavorUsage.Name)
				if !strings.HasPrefix(flavorName, ResourceFlavorPrefix) {
					continue
				}

				// 提取GPU简称 (去掉mlops-gpu-前缀)
				gpuModel := strings.TrimPrefix(flavorName, ResourceFlavorPrefix)

				// 在resources中查找GPU资源标签对应的total值
				for _, resource := range flavorUsage.Resources {
					if resource.Name == v1.ResourceName(gpuResourceKey) {
						// 解析total值并除以100
						totalQuantity := resource.Total.Value()
						gpuCards := float64(totalQuantity) / 100.0
						result[gpuModel] += gpuCards
						break
					}
				}
			}
		}
	}

	return toGPUType(result), nil
}

// GetClusterGpuPendingResources 获取多个集群的所有带有managed-by=mlops标签的ClusterQueue的等待GPU资源和时间
// 入参: clusters []string - 集群名称列表, teamId int - 团队ID(可选，0表示不过滤团队)
// 出参: map[string]float64 - key为GPU简称(如l20)，value为GPU卡数, uint - 等待时间总和(秒)
func GetClusterGpuPendingResources(ctx context.Context, clusters []string, teamId int) (map[string]float64, uint, error) {
	result := make(map[string]float64)
	var totalWaitingTimeSeconds uint = 0

	for _, cluster := range clusters {
		// 获取集群类型
		clusterType := GetClusterType(cluster)
		if clusterType == "" {
			log.L.WithName("GetClusterGpuPendingResources").Warningf(ctx, "Unknown cluster type for cluster: %s", cluster)
			continue
		}

		// 根据集群类型获取GPU资源标签
		gpuResourceKey := GetGpuResourceKeyByType(clusterType)
		if gpuResourceKey == "" {
			log.L.WithName("GetClusterGpuPendingResources").Warningf(ctx, "No GPU resource key for cluster type: %s", clusterType)
			continue
		}

		// 构建标签选择器，直接获取带有managed-by=mlops标签的ClusterQueue
		labelSelectors := []constack_openapi.LabelSelector{
			{
				Key:   ManagedByLabel,
				Op:    "=",
				Value: ManagedByValue,
			},
		}

		// 如果指定了teamId，添加团队标签过滤
		if teamId > 0 {
			labelSelectors = append(labelSelectors, constack_openapi.LabelSelector{
				Key:   TeamIdLabel,
				Op:    "=",
				Value: fmt.Sprintf("%d", teamId),
			})
		}

		// 获取带有指定标签的ClusterQueue
		clusterQueues, err := client.ListClusterQueue(ctx, cluster, labelSelectors)
		if err != nil {
			log.L.WithName("GetClusterGpuPendingResources").Errorf(ctx, "Failed to list cluster queues for cluster %s: %s", cluster, err.Error())
			continue
		}

		// 循环处理每个ClusterQueue的等待工作负载
		for _, cq := range clusterQueues {
			// 检查是否有等待的工作负载
			if cq.Status.PendingWorkloads <= 0 {
				continue
			}

			// 调用k8s_apiproxy获取PendingWorkloadsItems
			req := &client.PendingWorkloadsRequest{
				ClusterName:      cluster,
				ClusterQueueName: cq.Name,
			}

			pendingItems, err := client.K8sApiProxy.GetPendingWorkloadsItems(ctx, req)
			if err != nil {
				log.L.WithName("GetClusterGpuPendingResources").Errorf(ctx, "Failed to get pending workloads for cluster %s clusterqueue %s: %s", cluster, cq.Name, err.Error())
				continue
			}

			// 计算这个ClusterQueue的等待时间
			clusterQueueWaitingTime := uint(len(pendingItems)) * FixedPendingTimeSeconds
			totalWaitingTimeSeconds += clusterQueueWaitingTime

			// 遍历每个等待的工作负载
			for _, pendingItem := range pendingItems {
				// 通过name和namespace获取Workload详情
				workload, err := client.GetWorkload(ctx, cluster, pendingItem.Namespace, pendingItem.Name)
				if err != nil {
					log.L.WithName("GetClusterGpuPendingResources").Warningf(ctx, "Failed to get workload %s/%s: %s", pendingItem.Namespace, pendingItem.Name, err.Error())
					continue
				}

				// 从workload.spec.podSets中提取GPU模型
				var gpuModel string
				for _, podSet := range workload.Spec.PodSets {
					if podSet.Template.Spec.Affinity != nil &&
						podSet.Template.Spec.Affinity.NodeAffinity != nil &&
						podSet.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution != nil {

						for _, nodeSelectorTerm := range podSet.Template.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
							for _, matchExpression := range nodeSelectorTerm.MatchExpressions {
								if matchExpression.Key == NodeLabelGpuModel && matchExpression.Operator == "In" && len(matchExpression.Values) > 0 {
									gpuModel = matchExpression.Values[0]
									break
								}
							}
							if gpuModel != "" {
								break
							}
						}
					}
					if gpuModel != "" {
						break
					}
				}

				if gpuModel == "" {
					log.L.WithName("GetClusterGpuPendingResources").Warningf(ctx, "No gpu-model found in workload %s/%s", pendingItem.Namespace, pendingItem.Name)
					continue
				}

				// 从workload.status.resourceRequests中提取GPU资源需求
				for _, resourceRequest := range workload.Status.ResourceRequests {
					if resourceRequest.Resources != nil {
						if gpuResource, exists := resourceRequest.Resources[v1.ResourceName(gpuResourceKey)]; exists {
							// 解析GPU资源值并除以100
							gpuValue := gpuResource.Value()
							gpuCards := float64(gpuValue) / 100.0
							result[gpuModel] += gpuCards
						}
					}
				}
			}
		}
	}

	return toGPUType(result), totalWaitingTimeSeconds, nil
}

// GetModelGpuUsedResources 获取多个集群的所有带有managed-by=mlops标签的LocalQueue的在使用的GPU资源
// 入参: clusters []string - 集群名称列表, teamId int - 团队ID(可选，0表示不过滤团队), modelType string - 模型类型(可选，空字符串表示不过滤模型类型)
// 出参: map[string]float64 - key为GPU简称(如l20)，value为GPU卡数(total除以100)
func GetModelGpuUsedResources(ctx context.Context, clusters []string, teamId int, modelType string) (map[string]float64, error) {
	result := make(map[string]float64)

	for _, cluster := range clusters {
		// 获取集群类型
		clusterType := GetClusterType(cluster)
		if clusterType == "" {
			log.L.WithName("GetModelGpuUsedResources").Warningf(ctx, "Unknown cluster type for cluster: %s", cluster)
			continue
		}

		// 根据集群类型获取GPU资源标签
		gpuResourceKey := GetGpuResourceKeyByType(clusterType)
		if gpuResourceKey == "" {
			log.L.WithName("GetModelGpuUsedResources").Warningf(ctx, "No GPU resource key for cluster type: %s", clusterType)
			continue
		}

		// 构建标签选择器，直接获取带有managed-by=mlops标签的LocalQueue
		labelSelectors := []constack_openapi.LabelSelector{
			{
				Key:   ManagedByLabel,
				Op:    "=",
				Value: ManagedByValue,
			},
		}

		// 如果指定了teamId，添加团队标签过滤
		if teamId > 0 {
			labelSelectors = append(labelSelectors, constack_openapi.LabelSelector{
				Key:   TeamIdLabel,
				Op:    "=",
				Value: fmt.Sprintf("%d", teamId),
			})
		}

		// 如果指定了modelType，添加模型类型标签过滤
		if modelType != "" {
			labelSelectors = append(labelSelectors, constack_openapi.LabelSelector{
				Key:   ModelTypeLabel,
				Op:    "=",
				Value: modelType,
			})
		}

		// 获取带有指定标签的LocalQueue（空字符串表示所有namespace）
		localQueues, err := client.ListLocalQueue(ctx, cluster, "", labelSelectors)
		if err != nil {
			log.L.WithName("GetModelGpuUsedResources").Errorf(ctx, "Failed to list local queues for cluster %s: %s", cluster, err.Error())
			continue
		}

		// 循环处理每个LocalQueue的status.flavorUsage
		for _, lq := range localQueues {
			if lq.Status.FlavorUsage == nil {
				continue
			}

			for _, flavorUsage := range lq.Status.FlavorUsage {
				// 检查flavor name是否以ResourceFlavorPrefix开头
				flavorName := string(flavorUsage.Name)
				if !strings.HasPrefix(flavorName, ResourceFlavorPrefix) {
					continue
				}

				// 提取GPU简称 (去掉mlops-gpu-前缀)
				gpuModel := strings.TrimPrefix(flavorName, ResourceFlavorPrefix)

				// 在resources中查找GPU资源标签对应的total值
				for _, resource := range flavorUsage.Resources {
					if resource.Name == v1.ResourceName(gpuResourceKey) {
						// 解析total值并除以100
						totalQuantity := resource.Total.Value()
						gpuCards := float64(totalQuantity) / 100.0
						result[gpuModel] += gpuCards
						break
					}
				}
			}
		}
	}

	return toGPUType(result), nil
}

// toGPTType 将GPU模型简称转换为完整的GPU类型名称
// 入参: gpuModels map[string]float64 - key为GPU简称(如l20)，value为GPU卡数
// 出参: map[string]float64 - key为GPU完整名称(如NVIDIA L20)，value为GPU卡数(不变)
func toGPUType(gpuModels map[string]float64) map[string]float64 {
	result := make(map[string]float64)

	for gpuModel, count := range gpuModels {
		// 通过GPTModelMap查找对应的完整GPU类型名称
		if fullName, exists := GPUModelMap[gpuModel]; exists {
			result[fullName] = count
		} else {
			// 如果在映射中找不到，保持原始的key
			result[gpuModel] = count
		}
	}

	return result
}

// EnsureNamespaceLabel 确保 Namespace 具有指定的标签
func EnsureNamespaceLabel(ctx context.Context, clusterName, namespaceName string) error {
	// 获取现有的 Namespace
	namespace, err := client.GetNamespace(ctx, clusterName, namespaceName)
	if err != nil {
		return fmt.Errorf("failed to get namespace %s: %w", namespaceName, err)
	}

	// 检查标签是否已存在且值正确
	if namespace.Labels == nil {
		namespace.Labels = make(map[string]string)
	}

	if existingValue, exists := namespace.Labels[MemberLabel]; exists && existingValue == "true" {
		// 标签已存在且值正确，无需更新
		return nil
	}

	// 添加或更新标签
	namespace.Labels[MemberLabel] = "true"

	// 更新 Namespace
	return client.UpdateNamespace(ctx, clusterName, namespace)
}

// DisabledNamespaceLabel 禁用 Namespace 的成员标签
func DisabledNamespaceLabel(ctx context.Context, clusterName, namespaceName string) error {
	// 获取现有的 Namespace
	namespace, err := client.GetNamespace(ctx, clusterName, namespaceName)
	if err != nil {
		return fmt.Errorf("failed to get namespace %s: %w", namespaceName, err)
	}

	// 检查标签是否已存在且值正确
	if namespace.Labels == nil {
		namespace.Labels = make(map[string]string)
	}

	if existingValue, exists := namespace.Labels[MemberLabel]; exists && existingValue == "false" {
		// 标签已存在且值正确，无需更新
		return nil
	}

	// 添加或更新标签为 false
	namespace.Labels[MemberLabel] = "false"

	// 更新 Namespace
	return client.UpdateNamespace(ctx, clusterName, namespace)
}
