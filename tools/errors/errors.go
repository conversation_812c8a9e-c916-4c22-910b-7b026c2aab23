package errors

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

var (
	CodeForbidden    = localCode{100, "Forbidden Request", nil}
	CodeDuplicate    = localCode{200, "Duplicate Entry Error", nil}
	CodeCorsResource = localCode{201, "Cors Resource Error", nil}
	CodeInvalidGpu   = localCode{202, "Invalid Gpu Type Error", nil}
)

type localCode struct {
	code    int         // Error code, usually an integer.
	message string      // Brief message for this error code.
	detail  interface{} // As type of interface, it is mainly designed as an extension field for error code.
}

// Code returns the integer number of current error code.
func (c localCode) Code() int {
	return c.code
}

// Message returns the brief message for current error code.
func (c localCode) Message() string {
	return c.message
}

// Detail returns the detailed information of current error code,
// which is mainly designed as an extension field for error code.
func (c localCode) Detail() interface{} {
	return c.detail
}

func ValidateError(text ...string) error {
	return gerror.NewCode(gcode.CodeValidationFailed, text...)
}

func ForbiddenError(text ...string) error {
	return gerror.NewCode(CodeForbidden, text...)
}

func IsNotFoundError(err error) bool {
	return gerror.Code(err) == gcode.CodeNotFound
}

func NewError(code gcode.Code, text ...string) error {
	return gerror.NewCode(code, text...)
}
